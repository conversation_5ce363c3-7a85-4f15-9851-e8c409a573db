<?php

namespace Tests\Feature\Admin;

use App\Models\Category;
use App\Models\Part;
use App\Models\SiteSetting;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PartIndexExportImportTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected Category $category;
    protected Part $part;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->admin()->create();
        $this->category = Category::factory()->create();
        $this->part = Part::factory()->create([
            'category_id' => $this->category->id,
        ]);
    }

    /** @test */
    public function index_page_passes_export_import_enabled_setting_when_enabled()
    {
        // Enable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', true);

        $response = $this->actingAs($this->admin)
            ->get('/admin/parts');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Parts/Index')
                ->has('exportImportEnabled')
                ->where('exportImportEnabled', true)
        );
    }

    /** @test */
    public function index_page_passes_export_import_enabled_setting_when_disabled()
    {
        // Disable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', false);

        $response = $this->actingAs($this->admin)
            ->get('/admin/parts');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Parts/Index')
                ->has('exportImportEnabled')
                ->where('exportImportEnabled', false)
        );
    }

    /** @test */
    public function export_all_blocked_when_setting_disabled()
    {
        // Disable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', false);

        $response = $this->actingAs($this->admin)
            ->get('/admin/parts/export');

        $response->assertStatus(403);
        $response->assertJson(['error' => 'This feature is disabled for security reasons.']);
    }

    /** @test */
    public function export_all_works_when_setting_enabled()
    {
        // Enable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', true);

        $response = $this->actingAs($this->admin)
            ->get('/admin/parts/export');

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
    }

    /** @test */
    public function download_template_blocked_when_setting_disabled()
    {
        // Disable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', false);

        $response = $this->actingAs($this->admin)
            ->get('/admin/parts/template/download');

        $response->assertStatus(403);
        $response->assertJson(['error' => 'This feature is disabled for security reasons.']);
    }

    /** @test */
    public function download_template_works_when_setting_enabled()
    {
        // Enable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', true);

        $response = $this->actingAs($this->admin)
            ->get('/admin/parts/template/download');

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
    }

    /** @test */
    public function index_page_includes_required_props()
    {
        $response = $this->actingAs($this->admin)
            ->get('/admin/parts');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Parts/Index')
                ->has('parts')
                ->has('filters')
                ->has('queryParams')
                ->has('exportImportEnabled')
        );
    }
}
