<?php

namespace Tests\Feature\Admin;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\SiteSetting;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Tests\TestCase;

class ExportImportSettingScopeTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected Part $part;
    protected Brand $brand;
    protected MobileModel $model;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        // Create test data
        $category = Category::factory()->create(['name' => 'Test Category']);
        $this->brand = Brand::factory()->create(['name' => 'Apple']);
        $this->model = MobileModel::factory()->create([
            'name' => 'iPhone 13',
            'brand_id' => $this->brand->id,
        ]);
        $this->part = Part::factory()->create([
            'name' => 'Test Part',
            'category_id' => $category->id,
        ]);

        // Attach model to part with pivot data
        $this->part->models()->attach($this->model->id, [
            'display_type' => 'OLED',
            'display_size' => '6.1 inches',
            'location' => 'Front',
            'is_verified' => true,
            'is_compatible' => true,
            'compatibility_notes' => 'Fully compatible',
        ]);
    }

    /** @test */
    public function bulk_operations_are_blocked_when_setting_disabled()
    {
        // Disable export/import functionality
        SiteSetting::set('admin_parts_export_import_enabled', false);

        // Test bulk parts export
        $response = $this->actingAs($this->admin)
            ->get('/admin/parts/export');
        $response->assertStatus(403);

        // Test bulk parts template download
        $response = $this->actingAs($this->admin)
            ->get('/admin/parts/template/download');
        $response->assertStatus(403);
    }

    /** @test */
    public function bulk_operations_work_when_setting_enabled()
    {
        // Enable export/import functionality
        SiteSetting::set('admin_parts_export_import_enabled', true);

        // Test bulk parts export
        $response = $this->actingAs($this->admin)
            ->get('/admin/parts/export');
        $response->assertStatus(200);

        // Test bulk parts template download
        $response = $this->actingAs($this->admin)
            ->get('/admin/parts/template/download');
        $response->assertStatus(200);
    }

    /** @test */
    public function compatibility_operations_always_work_regardless_of_setting()
    {
        // Test with setting disabled
        SiteSetting::set('admin_parts_export_import_enabled', false);

        // Test single part export (compatibility CSV export)
        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}/export");
        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');

        // Test compatibility template download
        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}/compatibility/template");
        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');

        // Test compatibility import preview
        $csvContent = "Brand,Model,Compatible\nApple,iPhone 13,true";
        $tempFile = tmpfile();
        fwrite($tempFile, $csvContent);
        $tempPath = stream_get_meta_data($tempFile)['uri'];

        $response = $this->actingAs($this->admin)
            ->post("/admin/parts/{$this->part->id}/compatibility/preview", [
                'file' => new UploadedFile($tempPath, 'test.csv', 'text/csv', null, true)
            ]);
        $response->assertStatus(200);
        $response->assertJsonStructure(['success', 'columns', 'totalRows', 'previewRows']);

        fclose($tempFile);

        // Test compatibility import
        $csvContent = "Brand,Model,Compatible\nApple,iPhone 14,true";
        $tempFile = tmpfile();
        fwrite($tempFile, $csvContent);
        $tempPath = stream_get_meta_data($tempFile)['uri'];

        $response = $this->actingAs($this->admin)
            ->post("/admin/parts/{$this->part->id}/compatibility/import", [
                'file' => new UploadedFile($tempPath, 'test.csv', 'text/csv', null, true),
                'selected_columns' => ['brand', 'model', 'compatible']
            ]);
        $this->assertTrue(in_array($response->getStatusCode(), [200, 302]));

        fclose($tempFile);

        // Test with setting enabled - should still work
        SiteSetting::set('admin_parts_export_import_enabled', true);

        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}/export");
        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');

        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}/compatibility/template");
        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
    }

    /** @test */
    public function frontend_receives_correct_setting_values()
    {
        // Test index page receives the setting (for bulk operations)
        SiteSetting::set('admin_parts_export_import_enabled', true);
        
        $response = $this->actingAs($this->admin)
            ->get('/admin/parts');
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Parts/Index')
                ->has('exportImportEnabled')
                ->where('exportImportEnabled', true)
        );

        // Test show page does NOT receive the setting (compatibility operations are always available)
        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}");
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Parts/Show')
                ->missing('exportImportEnabled')
        );

        // Test compatibility page does NOT receive the setting
        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}/compatibility");
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Parts/Compatibility')
                ->missing('exportImportEnabled')
        );
    }
}
